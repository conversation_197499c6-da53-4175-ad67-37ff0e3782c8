from torcheeg.datasets import FACEDDataset
from torcheeg import transforms
from torch.utils.data import DataLoader
from torcheeg.trainers import ClassifierTrainer

from torcheeg.models import Conformer, LaBraM, ViT, CCNN, GETN
from torcheeg.datasets.constants import FACED_CHANNEL_LOCATION_DICT
import pytorch_lightning as pl
from torcheeg.model_selection import train_test_split_cross_subject, train_test_split_cross_trial, \
    train_test_split_groupby_trial
from pytorch_lightning.strategies import DDPStrategy
import torch

from torcheeg.transforms import after_hook_normalize
from torcheeg.transforms import before_hook_normalize



# 3 分类的 FACED
def map_emotion_to_valence(emotion):
    # 根据 FACED 数据集的情绪类别映射
    # 0=愤怒, 1=厌恶, 2=恐惧, 3=悲伤 -> 消极情绪 (0)
    # 4=中性 -> 中性情绪 (1)
    # 5=愉悦, 6=激励, 7=喜悦, 8=温柔 -> 积极情绪 (2)
    if emotion in [0, 1, 2, 3]:  # 消极情绪
        return 0
    elif emotion == 4:  # 中性情绪
        return 1
    else:  # 积极情绪 (5, 6, 7, 8)
        return 2


# dataset = FACEDDataset(
#     # io_path=f'./Fully_FACED/faced_9',
#     io_path=f'./Fully_FACED/faced_3',
#     root_path='../dataset/Processed_data',
#     offline_transform=transforms.Compose([
#         # transforms.MinMaxNormalize(axis=-1, apply_to_baseline=True),
#         transforms.BandDifferentialEntropy(sampling_rate=250, apply_to_baseline=True),
#         transforms.ToGrid(FACED_CHANNEL_LOCATION_DICT),
#         # transforms.To2d(),
#     ]),
#     online_transform=transforms.Compose([
#         transforms.BaselineRemoval(),
#         transforms.ToTensor()
#     ]),
#     label_transform=transforms.Compose([
#         transforms.Select('emotion'),
#         # transforms.Lambda(lambda x: x + 1)
#         transforms.Lambda(lambda x: map_emotion_to_valence(x)),
#     ]),
#     num_worker=16,
#     verbose=False
# )

dataset = FACEDDataset(
    # io_path=f'./Fully_FACED/faced_3',
    io_path=f'./Fully_FACED/faced_graph_3_afterhook',
    root_path='../dataset/Processed_data',
    chunk_size=2500, # duration = 10s
    offline_transform=transforms.Compose([
        transforms.BandDifferentialEntropy(sampling_rate=250, apply_to_baseline=True),
    ]),
    online_transform=transforms.Compose([
        transforms.ToTensor(),
        transforms.BaselineRemoval(),
    ]),
    after_trial=after_hook_normalize,
    label_transform=transforms.Compose([
        transforms.Select('emotion'),
        transforms.Lambda(lambda x: map_emotion_to_valence(x)),
    ]),
    num_worker=16,
    verbose=False
)

train_dataset, val_dataset = train_test_split_groupby_trial(
    dataset=dataset,
    test_size=0.2,
    split_path=f'./Fully_FACED/split/faced',
    shuffle=True,
    random_state=42
)

print(f"  - 训练集总样本数: {len(train_dataset)}")
print(f"  - 验证集总样本数: {len(val_dataset)}")
print(f"  - 总样本数: {len(train_dataset) + len(val_dataset)}")

train_loader = DataLoader(train_dataset, batch_size=64, num_workers=8, shuffle=True)
val_loader = DataLoader(val_dataset, batch_size=64, num_workers=8, shuffle=False)

# model = ViT(
#     chunk_size=250,
#     grid_size=(8, 9),  # 适合电极布局
#     t_patch_size=1,  # 时间维度的patch大小 如果提了 DE 则设置成 1
#     s_patch_size=(2, 3),  # 空间维度的patch大小
#     hid_channels=64,  # 隐藏层维度
#     depth=6,
#     heads=4,
#     head_channels=64,
#     mlp_channels=64,
#     num_classes=10
# )

# model = CCNN(
#     num_classes=10, in_channels=4, grid_size=(8, 9)
# )

for batch in train_loader:
    x, y = batch
    print(f"训练数据形状: {[x.shape for x in x]}")
    print(f"标签形状: {y.shape}")
    break

model = GETN(in_channels=4, num_electrodes=30,
             cheby_out_channels=16, cheby_num_layers=1,
             num_classes=3, hidden_dim=128,
             num_heads=8, num_layers=2,
             dropout=0.3)

trainer = ClassifierTrainer(model=model,
                            num_classes=3,
                            lr=1e-4,
                            weight_decay=1e-4,
                            devices=10,
                            accelerator="gpu",
                            # metrics=['accuracy', 'precision',
                            #          'recall', 'f1score', 'auroc']
                            )

trainer.fit(train_loader,
            val_loader,
            max_epochs=100,
            strategy=DDPStrategy(find_unused_parameters=True),
            default_root_dir=f'./Fully_FACED/model',
            callbacks=[pl.callbacks.ModelCheckpoint(save_last=True)],
            enable_progress_bar=True,
            enable_model_summary=True,
            limit_val_batches=0.2)

score = trainer.test(val_loader,
                     enable_progress_bar=True,
                     enable_model_summary=True)[0]

print(f'Test accuracy: {score["test_balanced_accuracy"]:.4f}')
