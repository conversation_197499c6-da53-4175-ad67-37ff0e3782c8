import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import GATConv, GCNConv
from torch_geometric.utils import from_scipy_sparse_matrix
import scipy.sparse as sp


class EEG_GCN_Transformer(nn.Module):
    def __init__(self, num_channels, num_bands, num_classes, hidden_dim=64, num_heads=4, num_layers=2, dropout=0.1):
        super(EEG_GCN_Transformer, self).__init__()
        self.gcn = GCNConv(num_bands, hidden_dim)
        self.embedding = nn.Linear(hidden_dim, hidden_dim)
        self.transformer_encoder = nn.TransformerEncoder(
            nn.TransformerEncoderLayer(hidden_dim, num_heads, hidden_dim, dropout),
            num_layers
        )
        # self.gat = GATConv(hidden_dim, hidden_dim)
        self.fc1 = nn.Linear(num_channels * hidden_dim, num_channels * hidden_dim // 2)
        self.fc2 = nn.Linear(num_channels * hidden_dim // 2, num_classes)
        self.activation = nn.GELU()

    def forward(self, x, edge_index, edge_weight):
        # x shape: (batch_size, num_channels, num_bands)
        batch_size, num_channels, num_bands = x.shape

        # Reshape for GCN
        x = x.reshape(batch_size * num_channels, num_bands)

        # GCN layer with edge weights
        x = self.gcn(x, edge_index, edge_weight)
        # x = F.relu(x)
        x = self.activation(x)

        # Reshape for Transformer
        x = x.reshape(batch_size, num_channels, -1)
        x = self.embedding(x)
        x = x.permute(1, 0, 2)  # (seq_len, batch_size, features)
        x = self.transformer_encoder(x)
        x = x.permute(1, 0, 2)  # (batch_size, seq_len, features)
        # x = self.gat(x, edge_index, edge_weight)
        x = x.reshape(x.size(0), -1)  # Flatten for classification
        x = self.fc1(x)
        x = self.activation(x)
        x = self.fc2(x)
        return x


class GraphConvolution(nn.Module):
    def __init__(self, in_channels: int, out_channels: int, bias: bool = False):

        super(GraphConvolution, self).__init__()

        self.in_channels = in_channels
        self.out_channels = out_channels
        self.weight = nn.Parameter(torch.FloatTensor(in_channels, out_channels))
        nn.init.xavier_normal_(self.weight)
        self.bias = None
        if bias:
            self.bias = nn.Parameter(torch.FloatTensor(out_channels))
            nn.init.zeros_(self.bias)

    def forward(self, x: torch.Tensor, adj: torch.Tensor) -> torch.Tensor:
        out = torch.matmul(adj, x)
        out = torch.matmul(out, self.weight)
        if self.bias is not None:
            return out + self.bias
        else:
            return out


class GraphAttention(nn.Module):
    def __init__(self, in_channels: int, out_channels: int, bias: bool = False):
        super(GraphAttention, self).__init__()

        self.in_channels = in_channels
        self.out_channels = out_channels

        # 使用GATConv替代原来的简单图卷积
        self.gat = GATConv(
            in_channels=in_channels,
            out_channels=out_channels,
            heads=1,  # 设置注意力头数为1，保持输出维度不变
            dropout=0.1,  # 可以根据需要调整dropout率
            bias=bias
        )

    def forward(self, x: torch.Tensor, adj: torch.Tensor) -> torch.Tensor:
        # GAT需要edge_index格式的输入，需要将邻接矩阵转换为边索引
        edge_index = adj.nonzero().t()  # 将邻接矩阵转换为边索引格式
        return self.gat(x, edge_index)


class Linear(nn.Module):
    def __init__(self, in_channels: int, out_channels: int, bias: bool = True):
        super(Linear, self).__init__()
        self.linear = nn.Linear(in_channels, out_channels, bias=bias)
        nn.init.xavier_normal_(self.linear.weight)
        if bias:
            nn.init.zeros_(self.linear.bias)

    def forward(self, inputs: torch.Tensor) -> torch.Tensor:
        return self.linear(inputs)


def normalize_A(A: torch.Tensor, symmetry: bool = False) -> torch.Tensor:
    # A = F.relu(A)
    activation = nn.GELU()
    A = activation(A)
    if symmetry:
        A = A + torch.transpose(A, 0, 1)
        d = torch.sum(A, 1)
        d = 1 / torch.sqrt(d + 1e-10)
        D = torch.diag_embed(d)
        L = torch.matmul(torch.matmul(D, A), D)
    else:
        d = torch.sum(A, 1)
        d = 1 / torch.sqrt(d + 1e-10)
        D = torch.diag_embed(d)
        L = torch.matmul(torch.matmul(D, A), D)
    return L


def generate_cheby_adj(A: torch.Tensor, num_layers: int) -> torch.Tensor:
    support = []
    for i in range(num_layers):
        if i == 0:
            support.append(torch.eye(A.shape[1]).to(A.device))
        elif i == 1:
            support.append(A)
        else:
            temp = torch.matmul(support[-1], A)
            support.append(temp)
    return support


class Chebynet(nn.Module):
    def __init__(self, in_channels: int, num_layers: int, out_channels: int):
        super(Chebynet, self).__init__()
        self.num_layers = num_layers
        self.gc1 = nn.ModuleList()
        for i in range(num_layers):
            self.gc1.append(GraphConvolution(in_channels, out_channels))

    def forward(self, x: torch.Tensor, L: torch.Tensor) -> torch.Tensor:
        adj = generate_cheby_adj(L, self.num_layers)
        for i in range(len(self.gc1)):
            if i == 0:
                result = self.gc1[i](x, adj[i])
            else:
                result = result + self.gc1[i](x, adj[i])
        result = F.relu(result)
        return result


class GETN(nn.Module):
    r'''
    Dynamical Graph Convolutional Neural Networks (emb_DGCNN). For more details, please refer to the following information.

    - Paper: Song T, Zheng W, Song P, et al. EEG emotion recognition using dynamical graph convolutional neural networks[J]. IEEE Transactions on Affective Computing, 2018, 11(3): 532-541.
    - URL: https://ieeexplore.ieee.org/abstract/document/8320798
    - Related Project: https://github.com/xueyunlong12589/DGCNN

    Below is a recommended suite for use in emotion recognition tasks:

    .. code-block:: python

        from torcheeg.models import DGCNN
        from torcheeg.datasets import SEEDDataset
        from torcheeg import transforms

        dataset = SEEDDataset(root_path='./Preprocessed_EEG',
                              offline_transform=transforms.BandDifferentialEntropy(band_dict={
                                  "delta": [1, 4],
                                  "theta": [4, 8],
                                  "alpha": [8, 14],
                                  "beta": [14, 31],
                                  "gamma": [31, 49]
                              }),
                              online_transform=transforms.Compose([
                                  transforms.ToTensor()
                              ]),
                              label_transform=transforms.Compose([
                                  transforms.Select('emotion'),
                                  transforms.Lambda(lambda x: x + 1)
                              ]))

        model = DGCNN(in_channels=5, num_electrodes=62, cheby_out_channels=32, cheby_num_layers=2, num_classes=2)

        x, y = next(iter(DataLoader(dataset, batch_size=64)))
        model(x)

    Args:
        in_channels (int): The feature dimension of each electrode. (default: :obj:`5`)
        num_electrodes (int): The number of electrodes. (default: :obj:`62`)
        cheby_num_layers (int): The number of graph convolutional layers. (default: :obj:`2`)
        cheby_out_channels (int): The number of output features in the chebynet. (default: :obj:`32`)
        num_classes (int): The number of classes to predict. (default: :obj:`2`)
    '''

    def __init__(self,
                 in_channels: int = 4,
                 num_electrodes: int = 32,
                 cheby_num_layers: int = 2,
                 cheby_out_channels: int = 32,
                 num_classes: int = 5,
                 # for class EEG_GCN_transformer
                 hidden_dim: int = 64,
                 num_heads: int = 4,
                 num_layers: int = 2,
                 dropout: float = 0.1):
        super(GETN, self).__init__()
        self.in_channels = in_channels
        self.num_electrodes = num_electrodes
        self.cheby_out_channels = cheby_out_channels
        self.cheby_num_layers = cheby_num_layers
        self.num_classes = num_classes

        self.embedding = nn.Linear(num_electrodes * in_channels, num_electrodes * in_channels)
        self.layer1 = Chebynet(2 * in_channels, cheby_num_layers, cheby_out_channels)
        self.BN1 = nn.BatchNorm1d(in_channels)
        self.fc1 = Linear(num_electrodes * cheby_out_channels, 64)
        self.fc2 = Linear(64, num_classes)
        self.A = nn.Parameter(torch.FloatTensor(num_electrodes, num_electrodes))
        nn.init.xavier_normal_(self.A)

        self.layer2 = EEG_GCN_Transformer(num_channels=num_electrodes, num_bands=in_channels, num_classes=num_classes,
                                          hidden_dim=64, num_heads=4, num_layers=2, dropout=0.1)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        r'''
        Args:
            x (torch.Tensor): EEG signal representation, the ideal input shape is :obj:`[n, 32, 4]`. Here, :obj:`n` corresponds to the batch size, :obj:`32` corresponds to :obj:`num_electrodes`, and :obj:`4` corresponds to :obj:`in_channels`.

        Returns:
            torch.Tensor[number of sample, number of classes]: the predicted probability that the samples belong to the classes.
        '''
        B, N, C = x.shape
        #  bacthnorm on C
        x = self.BN1(x.transpose(1, 2)).transpose(1, 2)
        # print(x[0].min(), x[0].max())
        # reshape to (B, N*C)
        x = x.reshape(B, -1)

        # embedding to (B, 2*N*C)
        emb_x = self.embedding(x)
        # reshape to (B, N, 2*C)
        emb_x = emb_x.reshape(B, N, C)

        L = normalize_A(self.A)

        adj = generate_cheby_adj(L, 2)
        adj1 = adj[0].detach().cpu().numpy()

        adj_sparse = sp.csr_matrix(adj1)
        edge_index, edge_weight = from_scipy_sparse_matrix(adj_sparse)
        edge_index = edge_index.to(x.device)
        edge_weight = edge_weight.to(x.device)

        result = self.layer2(emb_x, edge_index, edge_weight)

        return result
