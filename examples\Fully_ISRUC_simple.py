from torcheeg.datasets import ISRUCDataset
from torcheeg import transforms
from torcheeg.model_selection import train_test_split_cross_subject

import torch
from torch.utils.data import DataLoader

from torcheeg.models import EEGNet
from pytorch_lightning.strategies import DDPStrategy
import pytorch_lightning as pl

from torcheeg.trainers import ClassifierTrainer

dataset = ISRUCDataset(root_path='/media/Data3/yifanwang/ISRUC-SLEEP',
                       io_path=f'./Fully_ISRUC/isruc',
                       sfreq=200,
                       channels=['F3-A2', 'C3-A2', 'O1-A2',
                                 'F4-A1', 'C4-A1', 'O2-A1'],
                       offline_transform=transforms.Compose([
                           transforms.MeanStdNormalize(axis=-1),  # 使用更稳定的标准化
                           transforms.To2d()
                       ]),
                       online_transform=transforms.Compose([
                           transforms.ToTensor(),
                       ]),
                       label_transform=transforms.Compose([
                           transforms.Select('label'),
                           transforms.Mapping({'Sleep stage W': 0,
                                               'Sleep stage N1': 1,
                                               'Sleep stage N2': 2,
                                               'Sleep stage N3': 3,
                                               'Sleep stage R': 4,
                                               'Lights off@@EEG F4-A1': 0})
                       ]),
                       num_worker=16,
                       verbose=False
                       )

# ✅ 使用cross_subject分割 - 唯一适合ISRUC数据集的方法
# 原因：ISRUC每个被试只有一个试次(trial_id=0)，cross_trial和groupby_trial都不适用
train_dataset, val_dataset = train_test_split_cross_subject(
    dataset=dataset,
    test_size=0.2,
    split_path=f'./Fully_ISRUC/split/isruc',
    shuffle=True,
    random_state=42
)

print(f"  - 训练集总样本数: {len(train_dataset)}")
print(f"  - 验证集总样本数: {len(val_dataset)}")
print(f"  - 总样本数: {len(train_dataset) + len(val_dataset)}")

train_loader = DataLoader(train_dataset, batch_size=64, num_workers=8, shuffle=True)
val_loader = DataLoader(val_dataset, batch_size=64, num_workers=8, shuffle=False)

for batch in train_loader:
    x, y = batch
    print(f"训练数据形状: {x.shape}")
    print(f"标签形状: {y.shape}")
    break

# 根据ISRUC数据集的实际参数配置EEGNet
model = EEGNet(chunk_size=6000,  # 修改为实际的时间点数(30秒*200Hz)
               num_electrodes=6,  # 已正确设置为6个电极
               dropout=0.5,
               kernel_1=64,       # 保持原值，小于chunk_size
               kernel_2=16,       # 保持原值
               F1=8,
               F2=16,
               D=2,
               num_classes=5)     # 已正确设置为5类

trainer = ClassifierTrainer(model=model,
                            num_classes=5,
                            lr=1e-4,           # 降低学习率防止梯度爆炸
                            weight_decay=1e-4, # 保持较小的权重衰减
                            devices=10,         # 减少GPU数量提高稳定性
                            accelerator="gpu")

# 配置模型检查点 - 保存验证集上表现最好的模型
checkpoint_callback = pl.callbacks.ModelCheckpoint(
    monitor='val_balanced_accuracy',  # 监控验证集平衡准确率
    mode='max',                       # 最大化指标
    save_top_k=1,                     # 只保存最好的1个模型
    save_last=True,                   # 同时保存最后一个epoch（备用）
    filename='best-{epoch:02d}-{val_balanced_accuracy:.4f}',
    verbose=True
)

# 配置早停（可选）
early_stop_callback = pl.callbacks.EarlyStopping(
    monitor='val_balanced_accuracy',
    mode='max',
    patience=15,  # 15个epoch没有改善就停止
    verbose=True
)

trainer.fit(train_loader,
            val_loader,
            max_epochs=150,
            strategy=DDPStrategy(find_unused_parameters=True),
            default_root_dir=f'./Fully_ISRUC/model',
            callbacks=[checkpoint_callback, early_stop_callback],
            enable_progress_bar=True,
            enable_model_summary=True,
            limit_val_batches=0.5,
            gradient_clip_val=1.0)

score = trainer.test(val_loader, enable_progress_bar=True)[0]

# 显示训练过程中保存的最佳模型信息
if hasattr(checkpoint_callback, 'best_model_path') and checkpoint_callback.best_model_path:
    print(f"📁 最佳模型已保存至: {checkpoint_callback.best_model_path}")
    print(f"🏆 最佳验证准确率: {checkpoint_callback.best_model_score:.4f}")
