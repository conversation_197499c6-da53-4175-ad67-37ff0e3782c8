from torcheeg.model_selection import train_test_split_cross_trial, train_test_split_cross_subject, train_test_split_groupby_trial
from torcheeg.models import GETN
from torcheeg.datasets import DEAPDataset
from torcheeg import transforms

from torch.utils.data import DataLoader

from torcheeg.trainers import Classifier<PERSON>rainer
import pytorch_lightning as pl

from pytorch_lightning.strategies import DDPStrategy

dataset = DEAPDataset(root_path='../dataset/data_preprocessed_python',
                      io_path='./Fully_DEAP/deap',
                      offline_transform=transforms.BandDifferentialEntropy(band_dict={
                          "delta": [1, 4],
                          "theta": [4, 8],
                          "alpha": [8, 14],
                          "beta": [14, 31],
                          "gamma": [31, 49]
                      },
                          apply_to_baseline=True),
                      online_transform=transforms.Compose(
                          [transforms.BaselineRemoval(),
                           transforms.ToTensor()]),
                      # 4 classes
                      label_transform=transforms.Compose([
                          transforms.Select(['valence', 'arousal']),
                          transforms.Binary(5.0),
                          transforms.BinariesToCategory()
                      ]),
                      # 2 classes
                      #  label_transform=transforms.Compose([
                      #      transforms.Select('arousal'), # 或将 'arousal' 换成 'valence'
                      #      transforms.Binary(5.0),
                      #  ]),
                      chunk_size=128,
                      baseline_chunk_size=128,
                      num_baseline=3,
                      num_worker=8,
                      verbose=False)

train_dataset, val_dataset = train_test_split_cross_subject(
    dataset=dataset,
    test_size=0.2,
    shuffle=True,
    split_path=f'./Fully_DEAP/split/deap',
)

train_loader = DataLoader(train_dataset, batch_size=64, shuffle=True)
val_loader = DataLoader(val_dataset, batch_size=64, shuffle=False)

model = GETN(in_channels=5, num_electrodes=32,
             cheby_out_channels=16, cheby_num_layers=1,
             num_classes=4, hidden_dim=128,
             num_heads=8, num_layers=2,
             dropout=0.3)

trainer = ClassifierTrainer(model=model,
                            num_classes=4,  # 二分类要换成 2
                            lr=1e-4,
                            weight_decay=1e-3,
                            devices=10,
                            accelerator="gpu",
                            metrics=['accuracy', 'f1score']
                            )

trainer.fit(
    train_loader,
    val_loader,
    max_epochs=100,
    strategy=DDPStrategy(find_unused_parameters=True),
    default_root_dir=f'./Fully_DEAP/model',
    callbacks=[pl.callbacks.ModelCheckpoint(save_last=True)],
    enable_progress_bar=True,
    enable_model_summary=True,
    limit_val_batches=0.2
)
score = trainer.test(val_loader,
                     enable_progress_bar=True,
                     enable_model_summary=True)[0]

print(f'Test accuracy: {score["test_accuracy"]:.4f}')
