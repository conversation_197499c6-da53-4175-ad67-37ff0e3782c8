# 模型检查点配置指南

## 问题背景
默认情况下，PyTorch Lightning 使用最后一个 epoch 的权重进行测试，但这通常不是最优选择。我们希望使用在验证集上表现最好的模型进行最终测试。

## 解决方案

### 1. ModelCheckpoint 配置

```python
checkpoint_callback = pl.callbacks.ModelCheckpoint(
    monitor='val_balanced_accuracy',  # 监控的指标
    mode='max',                       # 最大化指标
    save_top_k=1,                     # 只保存最好的1个模型
    save_last=True,                   # 同时保存最后一个epoch（备用）
    filename='best-{epoch:02d}-{val_balanced_accuracy:.4f}',
    verbose=True
)
```

### 2. 关键参数说明

| 参数 | 说明 | 推荐值 |
|------|------|--------|
| `monitor` | 监控的验证指标 | `'val_balanced_accuracy'` |
| `mode` | 优化方向 | `'max'` (准确率) 或 `'min'` (损失) |
| `save_top_k` | 保存最好的k个模型 | `1` (只保存最佳) |
| `save_last` | 是否保存最后一个epoch | `True` (便于对比) |
| `filename` | 文件命名格式 | 包含epoch和指标值 |

### 3. 测试时加载最佳模型

```python
# 使用最佳模型测试
score = trainer.test(val_loader, ckpt_path='best')

# 使用最后一个epoch测试（对比）
score_last = trainer.test(val_loader, ckpt_path='last')
```

### 4. 常用监控指标

| 任务类型 | 推荐监控指标 | mode |
|----------|-------------|------|
| 分类任务 | `val_balanced_accuracy` | max |
| 分类任务 | `val_f1score` | max |
| 分类任务 | `val_loss` | min |
| 回归任务 | `val_mse` | min |
| 回归任务 | `val_mae` | min |

## 优势

1. **更好的泛化性能**: 避免过拟合，使用验证集最佳模型
2. **公平比较**: 确保不同实验使用相同的模型选择标准
3. **结果可重现**: 明确指定使用哪个checkpoint
4. **性能对比**: 可以比较最佳模型vs最后epoch的性能差异

## 注意事项

1. **监控指标必须存在**: 确保验证过程中计算了指定的指标
2. **早停配合**: 可以配合EarlyStopping使用，避免过度训练
3. **存储空间**: `save_top_k=1`可以节省存储空间
4. **命名规范**: 使用有意义的文件名便于后续分析

## 完整示例

```python
# 配置检查点
checkpoint_callback = pl.callbacks.ModelCheckpoint(
    monitor='val_balanced_accuracy',
    mode='max',
    save_top_k=1,
    save_last=True,
    filename='best-{epoch:02d}-{val_balanced_accuracy:.4f}',
    verbose=True
)

# 可选：配置早停
early_stop_callback = pl.callbacks.EarlyStopping(
    monitor='val_balanced_accuracy',
    mode='max',
    patience=10,
    verbose=True
)

# 训练
trainer.fit(
    train_loader,
    val_loader,
    callbacks=[checkpoint_callback, early_stop_callback]
)

# 测试最佳模型
best_score = trainer.test(val_loader, ckpt_path='best')[0]
print(f"Best model accuracy: {best_score['balanced_accuracy']:.4f}")
```
