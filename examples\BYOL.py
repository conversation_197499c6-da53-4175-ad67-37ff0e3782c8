import torch
from torch.utils.data import DataLoader, ConcatDataset
from torcheeg import transforms

from torcheeg.datasets import SEEDDataset, SEEDIVDataset
from torcheeg.datasets.constants import SEED_CHANNEL_LOCATION_DICT, SEED_IV_CHANNEL_LOCATION_DICT
from torcheeg.model_selection import train_test_split_groupby_trial
from torcheeg.models import ViT
from torcheeg.trainers import BYOLTrainer
import pytorch_lightning as pl
from einops import rearrange, repeat

from pytorch_lightning.strategies import DDPStrategy
import random
import numpy as np


def set_random_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)  # 如果有多个GPU

    # 保证cuDNN的确定性
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


set_random_seed(42)

contras_dataset_SEED = SEEDDataset(
    io_path=f'./BYOL_SEEDSEEDIV/seed',
    root_path='../dataset/Preprocessed_EEG',
    offline_transform=transforms.Compose([
        transforms.BandDifferentialEntropy(sampling_rate=200, apply_to_baseline=True),
        transforms.ToGrid(SEED_CHANNEL_LOCATION_DICT)
    ]),
    online_transform=transforms.Compose([
        transforms.ToTensor(),
        transforms.Contrastive(
            transforms.Compose([
                transforms.RandomMaskGrid(p=0.5),
                transforms.RandomPCANoise(p=0.5)
            ]),
            num_views=2)
    ]),
    chunk_size=200,
    num_worker=8,
    verbose=False
)

contras_dataset_SEEDIV = SEEDIVDataset(
    io_path=f'./BYOL_SEEDSEEDIV/seed_iv',
    root_path='../dataset/eeg_raw_data',
    offline_transform=transforms.Compose([
        transforms.BandDifferentialEntropy(sampling_rate=200, apply_to_baseline=True),
        transforms.ToGrid(SEED_IV_CHANNEL_LOCATION_DICT),
    ]),
    online_transform=transforms.Compose([
        transforms.ToTensor(),
        transforms.Contrastive(
            transforms.Compose([
                transforms.RandomMaskGrid(p=0.5),
                transforms.RandomPCANoise(p=0.5)
            ]),
            num_views=2)
    ]),
    chunk_size=200,
    num_worker=8,
    verbose=False
)

# 对 SEED 数据集进行训练集和测试集划分
train_SEED, val_SEED = train_test_split_groupby_trial(
    dataset=contras_dataset_SEED,
    test_size=0.2,
    split_path=f'./BYOL_SEEDSEEDIV/split_seed',
    shuffle=True
)

# 对 SEED-IV 数据集进行训练集和测试集划分
train_SEEDIV, val_SEEDIV = train_test_split_groupby_trial(
    dataset=contras_dataset_SEEDIV,
    test_size=0.2,
    split_path=f'./BYOL_SEEDSEEDIV/split_seediv',
    shuffle=True
)

train_combined_dataset = ConcatDataset([train_SEED, train_SEEDIV])
val_combined_dataset = ConcatDataset([val_SEED, val_SEEDIV])

train_loader = DataLoader(train_SEEDIV, batch_size=64, num_workers=11, shuffle=True)
val_loader = DataLoader(val_SEEDIV, batch_size=64, num_workers=11, shuffle=False)


# 使用修改后的 ViT 模型作为特征提取器
class ViTExtractor(ViT):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def forward(self, x, **kwargs):
        x = self.to_patch_embedding(x)
        x = rearrange(x, 'b ... d -> b (...) d')  # [batch_size, num_patches, hid_channels]
        b, n, _ = x.shape

        cls_tokens = repeat(self.cls_token, '1 1 d -> b 1 d', b=b)
        x = torch.cat((cls_tokens, x), dim=1)
        x += self.pos_embedding[:, :(n + 1)]
        x = self.dropout(x)

        x = self.transformer(x)

        # 返回所有token的特征，而不是只返回cls token或平均值
        # 可以选择返回所有token或只返回patch token（不包括cls token）
        if kwargs.get('return_patch_tokens', False):
            return x[:, 1:] if not kwargs.get('return_all_tokens', False) else x

        # 默认行为：返回cls token或平均池化结果
        x = x.mean(dim=1) if self.pool_func == 'mean' else x[:, 0]

        # 不经过分类头，直接返回特征
        return x


# 创建特征提取器
extractor = ViTExtractor(
    chunk_size=200,
    grid_size=(9, 9),  # 适合电极布局
    t_patch_size=1,  # 时间维度的patch大小
    hid_channels=64,  # 隐藏层维度
    depth=6,
    heads=4,
    head_channels=64,
    mlp_channels=64,
    num_classes=3  # 虽然不用于分类，但需要设置
)

# for batch in train_loader:
#     views, _ = batch  # BYOL 不使用标签
#     print(f"训练数据形状: {[view.shape for view in views]}")
#     # 检查特征提取器输出
#     with torch.no_grad():
#         features = extractor(views[0], return_patch_tokens=True)
#         print(f"特征提取器输出形状: {features.shape}")
#     break

trainer = BYOLTrainer(
    extractor,
    extract_channels=64,
    devices=10,
    lr=1e-4,
    accelerator='gpu',
    metrics=['acc_top1', 'acc_mean_pos']
)

trainer.fit(train_loader,
            val_loader,
            max_epochs=200,
            strategy=DDPStrategy(find_unused_parameters=True),
            default_root_dir=f'./BYOL_SEEDSEEDIV/model',
            callbacks=[pl.callbacks.ModelCheckpoint(save_last=True)],
            enable_progress_bar=True,
            enable_model_summary=True,
            limit_val_batches=1.0,
            )

model_save_path = './BYOL_SEEDSEEDIV/model/SEED+SEED4_vit.pth'
torch.save(extractor.state_dict(), model_save_path)
print(f"Model saved to {model_save_path}")
