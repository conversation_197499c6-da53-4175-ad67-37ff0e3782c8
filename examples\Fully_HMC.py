from torcheeg.datasets import HMCDataset
from torcheeg import transforms
from torcheeg.model_selection import train_test_split_cross_subject

from torch.utils.data import DataLoader

from torcheeg.models import EEGNet
from torcheeg.trainers import ClassifierTrainer

from pytorch_lightning.strategies import DDPStrategy
import pytorch_lightning as pl


dataset = HMCDataset(root_path='../dataset/HMC/recordings',
                     io_path='./Fully_HMC/hmc',
                     sfreq=200,
                     channels=['EEG F4-M1', 'EEG C4-M1',
                               'EEG O2-M1', 'EEG C3-M2'],
                     offline_transform=transforms.Compose([
                         transforms.MeanStdNormalize(axis=-1),  # 使用更稳定的标准化
                         transforms.To2d()
                     ]),
                     online_transform=transforms.Compose([
                         transforms.ToTensor(),
                     ]),
                     label_transform=transforms.Compose([
                         transforms.Select('label'),
                         transforms.Mapping({'Sleep stage W': 0,
                                             'Sleep stage N1': 1,
                                             'Sleep stage N2': 2,
                                             'Sleep stage N3': 3,
                                             'Sleep stage R': 4,
                                             'Lights off@@EEG F4-A1': 0})
                     ]),
                     num_worker=16,
                     verbose=False
                     )

train_dataset, val_dataset = train_test_split_cross_subject(
    dataset=dataset,
    test_size=0.2,
    split_path=f'./Fully_HMC/split/hmc',
    shuffle=True,
    random_state=42
)

print(f"  - 训练集总样本数: {len(train_dataset)}")
print(f"  - 验证集总样本数: {len(val_dataset)}")
print(f"  - 总样本数: {len(train_dataset) + len(val_dataset)}")


train_loader = DataLoader(train_dataset, batch_size=64, num_workers=8, shuffle=True)
val_loader = DataLoader(val_dataset, batch_size=64, num_workers=8, shuffle=False)

for batch in train_loader:
    x, y = batch
    print(f"训练数据形状: {x.shape}")
    print(f"标签形状: {y.shape}")
    break

model = EEGNet(chunk_size=6000,  # 修改为实际的时间点数(30秒*200Hz)
               num_electrodes=4,
               dropout=0.5,
               kernel_1=64,
               kernel_2=16,
               F1=8,
               F2=16,
               D=2,
               num_classes=5)

trainer = ClassifierTrainer(model=model,
                            num_classes=5,
                            lr=1e-4,
                            weight_decay=1e-4,
                            devices=10,
                            accelerator="gpu",
                            # metrics=['accuracy']
                            )

# 配置模型检查点 - 保存验证集上表现最好的模型
checkpoint_callback = pl.callbacks.ModelCheckpoint(
    monitor='val_balanced_accuracy',  # 监控验证集平衡准确率
    mode='max',                       # 最大化指标
    save_top_k=1,                     # 只保存最好的1个模型
    save_last=True,                   # 同时保存最后一个epoch（备用）
    filename='best-{epoch:02d}-{val_balanced_accuracy:.4f}',
    verbose=True
)

trainer.fit(train_loader,
            val_loader,
            max_epochs=150,
            strategy=DDPStrategy(find_unused_parameters=True),
            default_root_dir=f'./Fully_HMC/model',
            callbacks=[pl.callbacks.ModelCheckpoint(save_last=True)],
            enable_progress_bar=True,
            enable_model_summary=True,
            limit_val_batches=0.5,
            # gradient_clip_val=1.0 # 添加梯度裁剪防止梯度爆炸
            )

score = trainer.test(val_loader,
                     ckpt_path='best',  # 自动加载最佳模型
                     enable_progress_bar=True,
                     enable_model_summary=True)[0]
