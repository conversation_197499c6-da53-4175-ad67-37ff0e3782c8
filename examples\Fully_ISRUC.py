from torcheeg.datasets import ISRUCDataset
from torcheeg import transforms
from torcheeg.model_selection import train_test_split_cross_trial, train_test_split_groupby_trial, train_test_split_cross_subject

import torch
from torch.utils.data import DataLoader

from torcheeg.models import EEGNet, GETN, Conformer
from pytorch_lightning.strategies import DDPStrategy
import pytorch_lightning as pl

from torcheeg.models.transformer.conformer import ClassificationHead
from torcheeg.trainers import ClassifierTrainer

dataset = ISRUCDataset(root_path='/media/Data3/yifanwang/ISRUC-SLEEP',
                       io_path=f'./Fully_ISRUC/isruc',
                       sfreq=200,
                       # channels=['F3-A2', 'C3-A2', 'O1-A2',
                       #           'F4-A1', 'C4-A1', 'O2-A1'],
                       offline_transform=transforms.Compose([
                           transforms.MeanStdNormalize(axis=-1),  # 使用更稳定的标准化
                           transforms.To2d()
                       ]),
                       online_transform=transforms.Compose([
                           transforms.ToTensor(),
                       ]),
                       label_transform=transforms.Compose([
                           transforms.Select('label'),
                           transforms.Mapping({'Sleep stage W': 0,
                                               'Sleep stage N1': 1,
                                               'Sleep stage N2': 2,
                                               'Sleep stage N3': 3,
                                               'Sleep stage R': 4,
                                               'Lights off@@EEG F4-A1': 0})
                       ]),
                       num_worker=16,
                       verbose=False
                       )

# ✅ 使用cross_subject分割 - 唯一适合ISRUC数据集的方法
# 原因：ISRUC每个被试只有一个试次(trial_id=0)，cross_trial和groupby_trial都不适用
train_dataset, val_dataset = train_test_split_cross_subject(
    dataset=dataset,
    test_size=0.2,
    split_path=f'./Fully_ISRUC/split/isruc',
    shuffle=True,
    random_state=42
)

print(f"  - 训练集总样本数: {len(train_dataset)}")
print(f"  - 验证集总样本数: {len(val_dataset)}")
print(f"  - 总样本数: {len(train_dataset) + len(val_dataset)}")


train_loader = DataLoader(train_dataset, batch_size=64, num_workers=8, shuffle=True)
val_loader = DataLoader(val_dataset, batch_size=64, num_workers=8, shuffle=False)

# model = Conformer(num_electrodes=6,
#                   sampling_rate=200,
#                   hid_channels=40,
#                   depth=6,
#                   heads=10,
#                   dropout=0.5,
#                   forward_expansion=4,
#                   forward_dropout=0.5,
#                   num_classes=5)

# 添加这行代码来手动设置分类头
# model.cls = ClassificationHead(in_channels=15760,  # 使用错误信息中的实际维度
#                                num_classes=5,
#                                hid_channels=model.cls_channels,
#                                dropout=model.cls_dropout)

# 根据ISRUC数据集的实际参数配置EEGNet
# ISRUC数据: 6个电极，采样率200Hz，30秒数据=6000个时间点
model = EEGNet(chunk_size=6000,  # 修改为实际的时间点数(30秒*200Hz)
               num_electrodes=6,
               dropout=0.5,
               kernel_1=64,
               kernel_2=16,
               F1=8,
               F2=16,
               D=2,
               num_classes=5)

trainer = ClassifierTrainer(model=model,
                            num_classes=5,
                            lr=1e-4,
                            weight_decay=1e-4,
                            devices=10,
                            accelerator="gpu",
                            # metrics=['accuracy']
                            )

trainer.fit(train_loader,
            val_loader,
            max_epochs=150,
            strategy=DDPStrategy(find_unused_parameters=True),
            default_root_dir=f'./Fully_ISRUC/model',
            callbacks=[pl.callbacks.ModelCheckpoint(save_last=True)],
            enable_progress_bar=True,
            enable_model_summary=True,
            limit_val_batches=0.5,
            # gradient_clip_val=1.0 # 添加梯度裁剪防止梯度爆炸
            )

score = trainer.test(val_loader,
                     enable_progress_bar=True,
                     enable_model_summary=True, )[0]

print(f'balanced_accuracy: {score["balanced_accuracy"]:.4f}')